import { useState } from 'react'
import { Checkbox, Input, TableCell, TableRow } from '@libs/ui'
import type { MitigationPriority } from 'prime-front-service-client'
import type { MitigationItem } from '../types'
import { DatePicker, PrioritySelector } from './index'

interface MitigationTableRowProps {
  item: MitigationItem
  isSelected: boolean
  onToggleSelection: (id: number) => void
  onUpdateItem: (
    id: number,
    field: keyof MitigationItem,
    value: string | Date | MitigationPriority | null
  ) => void
}

export const MitigationTableRow = ({
  item,
  isSelected,
  onToggleSelection,
  onUpdateItem,
}: MitigationTableRowProps) => {
  const [editingTitle, setEditingTitle] = useState(false)
  const [editingDescription, setEditingDescription] = useState(false)
  const [titleValue, setTitleValue] = useState(item.title)
  const [descriptionValue, setDescriptionValue] = useState(item.description)

  const handleTitleSave = () => {
    onUpdateItem(item.id, 'title', titleValue)
    setEditingTitle(false)
  }

  const handleDescriptionSave = () => {
    onUpdateItem(item.id, 'description', descriptionValue)
    setEditingDescription(false)
  }

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSave()
    } else if (e.key === 'Escape') {
      setTitleValue(item.title)
      setEditingTitle(false)
    }
  }

  const handleDescriptionKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleDescriptionSave()
    } else if (e.key === 'Escape') {
      setDescriptionValue(item.description)
      setEditingDescription(false)
    }
  }

  return (
    <TableRow key={item.id}>
      <TableCell>
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onToggleSelection(item.id)}
        />
      </TableCell>
      <TableCell className="font-medium">
        {editingTitle ? (
          <Input
            value={titleValue}
            onChange={(e) => setTitleValue(e.target.value)}
            onBlur={handleTitleSave}
            onKeyDown={handleTitleKeyDown}
            autoFocus
            className="h-8"
          />
        ) : (
          <div
            onClick={() => setEditingTitle(true)}
            className="cursor-pointer hover:bg-muted/50 rounded px-2 py-1 -mx-2 -my-1"
            title="Click to edit"
          >
            {item.title}
          </div>
        )}
      </TableCell>
      <TableCell className="text-sm text-muted-foreground">
        {editingDescription ? (
          <Input
            value={descriptionValue}
            onChange={(e) => setDescriptionValue(e.target.value)}
            onBlur={handleDescriptionSave}
            onKeyDown={handleDescriptionKeyDown}
            autoFocus
            className="h-8"
          />
        ) : (
          <div
            onClick={() => setEditingDescription(true)}
            className="cursor-pointer hover:bg-muted/50 rounded px-2 py-1 -mx-2 -my-1"
            title="Click to edit"
          >
            {item.description}
          </div>
        )}
      </TableCell>
      <TableCell>
        <Input
          placeholder="Assignee"
          value={item.assignee || ''}
          onChange={(e) => onUpdateItem(item.id, 'assignee', e.target.value)}
        />
      </TableCell>
      <TableCell>
        <DatePicker
          value={item.due_date}
          onChange={(date) => onUpdateItem(item.id, 'due_date', date)}
          dataTestId="due-date-button"
        />
      </TableCell>
      <TableCell>
        <PrioritySelector
          value={item.priority}
          onChange={(priority) => onUpdateItem(item.id, 'priority', priority)}
          className="w-full"
        />
      </TableCell>
    </TableRow>
  )
}
