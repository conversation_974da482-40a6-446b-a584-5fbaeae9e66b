import { Checkbox, Input, TableCell, TableRow } from '@libs/ui'
import type { MitigationPriority } from 'prime-front-service-client'
import type { MitigationItem } from '../types'
import { DatePicker, PrioritySelector } from './index'

interface MitigationTableRowProps {
  item: MitigationItem
  isSelected: boolean
  onToggleSelection: (id: number) => void
  onUpdateItem: (
    id: number,
    field: keyof MitigationItem,
    value: string | Date | MitigationPriority | null
  ) => void
}

export const MitigationTableRow = ({
  item,
  isSelected,
  onToggleSelection,
  onUpdateItem,
}: MitigationTableRowProps) => {
  const handleTitleInput = (e: React.FormEvent<HTMLDivElement>) => {
    const newTitle = e.currentTarget.textContent || ''
    onUpdateItem(item.id, 'title', newTitle)
  }

  const handleDescriptionInput = (e: React.FormEvent<HTMLDivElement>) => {
    const newDescription = e.currentTarget.textContent || ''
    onUpdateItem(item.id, 'description', newDescription)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      e.currentTarget.blur()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      // Reset content to original value
      const target = e.currentTarget
      const isTitle = target.getAttribute('data-field') === 'title'
      target.textContent = isTitle ? item.title : item.description
      target.blur()
    }
  }

  return (
    <TableRow key={item.id}>
      <TableCell>
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onToggleSelection(item.id)}
        />
      </TableCell>
      <TableCell className="font-medium">
        <div
          contentEditable
          suppressContentEditableWarning
          onInput={handleTitleInput}
          onKeyDown={handleKeyDown}
          data-field="title"
          className="cursor-text hover:bg-muted/50 rounded px-2 py-1 -mx-2 -my-1 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          title="Click to edit"
        >
          {item.title}
        </div>
      </TableCell>
      <TableCell className="text-sm text-muted-foreground">
        <div
          contentEditable
          suppressContentEditableWarning
          onInput={handleDescriptionInput}
          onKeyDown={handleKeyDown}
          data-field="description"
          className="cursor-text hover:bg-muted/50 rounded px-2 py-1 -mx-2 -my-1 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          title="Click to edit"
        >
          {item.description}
        </div>
      </TableCell>
      <TableCell>
        <Input
          placeholder="Assignee"
          value={item.assignee || ''}
          onChange={(e) => onUpdateItem(item.id, 'assignee', e.target.value)}
        />
      </TableCell>
      <TableCell>
        <DatePicker
          value={item.due_date}
          onChange={(date) => onUpdateItem(item.id, 'due_date', date)}
          dataTestId="due-date-button"
        />
      </TableCell>
      <TableCell>
        <PrioritySelector
          value={item.priority}
          onChange={(priority) => onUpdateItem(item.id, 'priority', priority)}
          className="w-full"
        />
      </TableCell>
    </TableRow>
  )
}
